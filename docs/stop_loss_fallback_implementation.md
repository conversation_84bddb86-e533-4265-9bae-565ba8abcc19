# Реализация фолбэк логики для Stop Loss

## Описание проблемы

Ранее у бота не всегда получалось поставить stop loss ордер на бирже. В таких случаях stop loss сохранялся только локально, что создавало риск больших убытков при неконтролируемом движении цены.

## Решение

Реализована фолбэк логика с постепенным увеличением отступа от текущей рыночной цены:

1. **-1%** от текущей рыночной цены
2. **-2%** от текущей рыночной цены  
3. **-3%** от текущей рыночной цены
4. **-4%** от текущей рыночной цены
5. **-5%** от текущей рыночной цены

Если все попытки не удались, позиция **принудительно закрывается** и отправляется уведомление в специальный телеграм канал `errors_chat_id`.

## Новые методы

### `_create_stop_loss_with_fallback(trade: TradeInstance)`
Основной метод с фолбэк логикой:
- Сначала пробует оригинальный stop loss из сигнала
- При неудаче получает текущую рыночную цену
- Пробует фолбэк варианты с отступами 1-5%
- При всех неудачах вызывает `_handle_stop_loss_failure()`

### `_get_current_market_price(symbol: str) -> Optional[float]`
Получает текущую рыночную цену с биржи:
```python
ticker = self.exchange.fetch_ticker(symbol)
return float(ticker['last']) if ticker and ticker.get('last') else None
```

### `_calculate_fallback_stop_price(current_price: float, direction: SignalDirection, offset_percent: float) -> float`
Рассчитывает цену stop loss с отступом в процентах:
- **LONG**: `current_price * (1 - offset_percent / 100)` (ниже рыночной цены)
- **SHORT**: `current_price * (1 + offset_percent / 100)` (выше рыночной цены)

### `_try_create_stop_loss_order(trade: TradeInstance, stop_price: float, description: str) -> bool`
Пробует создать stop loss ордер с указанной ценой:
- Сначала пробует `STOP_MARKET` тип
- При неудаче пробует обычный `stop` тип
- Возвращает `True` при успехе, `False` при неудаче

### `_handle_stop_loss_failure(trade: TradeInstance, reason: str)`
Обрабатывает критическую неудачу установки stop loss:
- Логирует критическую ошибку
- Отправляет уведомление в `errors_chat_id` через `_send_to_errors_chat()`
- Принудительно закрывает позицию через `force_close_trade_on_exchange()`

## Логика работы

```mermaid
flowchart TD
    A[Создание Stop Loss] --> B[Пробуем оригинальный SL]
    B --> C{Успех?}
    C -->|Да| D[✅ Stop Loss установлен]
    C -->|Нет| E[Получаем рыночную цену]
    E --> F[Пробуем SL с отступом -1%]
    F --> G{Успех?}
    G -->|Да| D
    G -->|Нет| H[Пробуем SL с отступом -2%]
    H --> I{Успех?}
    I -->|Да| D
    I -->|Нет| J[Пробуем SL с отступом -3%]
    J --> K{Успех?}
    K -->|Да| D
    K -->|Нет| L[Пробуем SL с отступом -4%]
    L --> M{Успех?}
    M -->|Да| D
    M -->|Нет| N[Пробуем SL с отступом -5%]
    N --> O{Успех?}
    O -->|Да| D
    O -->|Нет| P[🚨 Критическая ошибка]
    P --> Q[Отправляем в errors_chat]
    P --> R[Закрываем позицию]
```

## Примеры расчетов

### LONG позиция
Текущая цена: $100.00
- **-1%**: Stop Loss = $99.00
- **-2%**: Stop Loss = $98.00
- **-3%**: Stop Loss = $97.00
- **-4%**: Stop Loss = $96.00
- **-5%**: Stop Loss = $95.00

### SHORT позиция  
Текущая цена: $100.00
- **+1%**: Stop Loss = $101.00
- **+2%**: Stop Loss = $102.00
- **+3%**: Stop Loss = $103.00
- **+4%**: Stop Loss = $104.00
- **+5%**: Stop Loss = $105.00

## Уведомления в errors_chat

При критической неудаче отправляется сообщение:
```
🚨 КРИТИЧЕСКАЯ ОШИБКА STOP LOSS

**Сделка:** trade_12345
**Символ:** BTC-USDT
**Направление:** Long
**Размер позиции:** 0.1
**Причина:** Все попытки установки stop loss не удались

**Действие:** Позиция принудительно закрыта для защиты от убытков
```

## Тестирование

Создан тест `test_stop_loss_fallback.py` который проверяет:
- ✅ Правильность расчета фолбэк цен
- ✅ Корректность отступов для LONG и SHORT позиций
- ✅ Все процентные отступы (1-5%)

## Безопасность

1. **Защита от больших убытков**: При невозможности установить stop loss позиция закрывается
2. **Мониторинг**: Все критические ошибки отправляются в `errors_chat_id`
3. **Логирование**: Подробные логи всех попыток установки stop loss
4. **Фолбэк**: Постепенное увеличение отступа повышает шансы успешной установки

## Интеграция

Изменения внесены в файл `trading_bot.py`:
- Метод `_create_stop_loss()` теперь вызывает `_create_stop_loss_with_fallback()`
- Добавлены 5 новых методов для фолбэк логики
- Сохранена обратная совместимость

Никаких изменений в других файлах не требуется.
