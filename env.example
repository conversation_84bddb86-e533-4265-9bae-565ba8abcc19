API_ID = 35898
API_HASH = ''
PHONE = # '+998908295593' # '+79169380360'
SESSION_NAME = 'user'
OUTPUT_FILE = 'user.txt'
ADMIN_ID = 50095099

# Глобальный целевой чат для пересылки сообщений (ID чата)
# НОВОЕ: Теперь каждый канал может иметь свой target_chat_id в SOURCE_CHANNELS
# Этот TARGET_CHAT_ID используется как fallback для каналов без индивидуального target_chat_id
# Если ни один не указан - канал работает в режиме мониторинга
# Примеры:
# TARGET_CHAT_ID = -4984770976  # Дельфины рынка (используется как fallback)
# TARGET_CHAT_ID = -4946229600  # канал Антона для пересылки новостей
# TARGET_CHAT_ID = -4816066301  # мой тест пересылки новостей
TARGET_CHAT_ID = -4984770976

# ИНДИВИДУАЛЬНЫЕ TARGET КАНАЛЫ
# Теперь каждый source канал может иметь свой target канал в конфигурации SOURCE_CHANNELS:
# "Канал": {
#     "id": -1001234567890,
#     "forward_type": "custom",
#     "target_chat_id": -4000000001  # Индивидуальный target для этого канала
# }
#
# Приоритеты:
# 1. target_chat_id канала (если указан) - высший приоритет
# 2. глобальный TARGET_CHAT_ID (если target_chat_id не указан)
# 3. режим мониторинга (если ни один не указан)

# Обработка длинных подписей (>1024 символов):
# 'separate' - отправлять длинный текст отдельным сообщением (по умолчанию)
# 'truncate' - обрезать подпись до 1024 символов
LONG_CAPTION_HANDLING = 'separate'

# Уровень логирования для отправки в ERRORS_CHAT_ID и админу в Telegram:
# 'ERROR' - только ошибки (по умолчанию)
# 'WARNING' - предупреждения и ошибки
# 'INFO' - вся информация (INFO, WARNING, ERROR, SUCCESS)
# 'ALL' - все уровни включая SUCCESS
ADMIN_LOG_LEVEL = 'ERROR'

# API ключ для анализа новостей и торговых сигналов с помощью ИИ
# Используется как для NewsAnalyzer, так и для SignalAnalyzer
# Получить можно на: https://openrouter.ai
OPENROUTER_API_KEY=your_api_key_here

# Включить/выключить LLM аналитику новостей
# True - включить аналитику (требует OPENROUTER_API_KEY)
# False - отключить аналитику (по умолчанию)
LLM_ANALYTICS_ENABLED=False

# Анализатор торговых сигналов настраивается через SOURCE_CHANNELS в index_v3.py
# Добавьте "signal_fn": "signal_analyzer" к каналу для извлечения JSON торговых сигналов
# Пример:
# "Акулы рынка": {"id": -1002667675217, "forward_type": "custom", "signal_fn": "signal_analyzer"}
# 
# Анализатор работает только с платными моделями для высокой точности:
# - openai/gpt-4o-mini (~$0.0001 за сигнал)
# - openai/gpt-4o
# - anthropic/claude-3.5-sonnet
# 
# Подробная документация: SIGNAL_ANALYZER.md

# Показывать заголовок с источником канала
# True - показывать заголовок "📢 Источник: Название канала"
# False - скрывать заголовок (по умолчанию)
SHOW_SOURCE_HEADER=False

# Получать список диалогов/контактов при запуске
# True - получать и сохранять список всех диалогов в файл
# False - пропускать получение диалогов (по умолчанию)
FETCH_DIALOGS_ENABLED=False

# API ключ для получения списка тикеров
X-CMC_PRO_API_KEY=your_coinmarketcap_api_key_here

# Санитизация текста для анализа новостей (замена конфликтных слов на нейтральные)
# True - включить санитизацию (по умолчанию, рекомендуется)
# False - отключить санитизацию (может вызвать блокировку некоторых моделей)
SANITIZE_NEWS_TEXT=True

# SOCKS5 proxy для OpenRouter API (необязательно)
# SOCKS5=True включает прокси, False отключает (по умолчанию)
SOCKS5=False
SOCKS5_ip=***************
SOCKS5_port=111
# Если прокси требует авторизацию (необязательно):
SOCKS5_USER=user
SOCKS5_PASS=pass

# Настройки для работы с медиафайлами
# Путь для скачивания медиафайлов (по умолчанию ./media)
DOWNLOAD_MEDIA_PATH=./media

# Удалять медиафайлы после отправки
# True - удалять скаченные файлы после успешной отправки
# False - оставлять файлы в папке (по умолчанию)
DELETE_MEDIA_AFTER_SEND=False

# Игнорировать скачивание видеофайлов
# True - не скачивать видео, отправлять только текст (по умолчанию)
# False - скачивать все типы медиа включая видео
IGNORE_VIDEO_DOWNLOAD=True

# === ТОРГОВАЯ СИСТЕМА ===
# Включить/отключить торговую систему
# True - включить автоматическую торговлю
# False - отключить торговлю (по умолчанию)
TRADING_ENABLED=False

# API ключи BingX для торговли
# Получить можно в личном кабинете BingX: https://bingx.com/
BINGX_API_KEY=your_bingx_api_key_here
BINGX_API_SECRET=your_bingx_api_secret_here

# Использовать тестовую среду BingX
# True - тестовая среда (рекомендуется для начала)
# False - реальная торговля
BINGX_TESTNET=True

# Максимальный процент портфеля для торговли (остальное - резерв)
# Например, 30 означает, что максимум 30% баланса будет использовано для торговли
# Остальные 70% останутся в резерве для безопасности
MAX_PORTFOLIO_USAGE=30

# Дополнительная конфигурация торговых ботов (JSON, необязательно)
# Переопределяет настройки из SOURCE_CHANNELS
# ⚠️ УСТАРЕЛО: Настройки теперь берутся из SOURCE_CHANNELS в index_v3.py
# Пример: {"Акулы рынка": {"max_volume": 100, "leverage": 15}}
# TRADING_BOT_CONFIGS={}

BALANCE_BOT_TOKEN=some

QDB_WEB_PORT=9001
QDB_PG_PORT=8813
QDB_INFLUX_TCP_PORT=9010
QDB_INFLUX_UDP_PORT=9010
QDB_HEALTH_PORT=9004
QDB_PG_USER=admin
QDB_PG_PASSWORD=pass
QDB_HTTP_MIN_ENABLED=true
QDB_HTTP_SECURITY_READONLY=false

# PostgreSQL Configuration
POSTGRES_HOST=***************
POSTGRES_PORT=5432
POSTGRES_USER=pguser
POSTGRES_PASSWORD=pgpass
POSTGRES_DB=pgdbname

# ID чата для логирования всех ошибок и предупреждений приложения
# Все логи (INFO, WARNING, ERROR, SUCCESS) будут отправляться в этот чат
# Если не задан - логи будут отправляться в ADMIN_ID
ERRORS_CHAT_ID = <chat for app errors and warnings>