# Автоматическая корректировка плеча

## Описание проблемы

В некоторых торговых парах минимальный размер лота настолько велик, что при использовании фиксированного плеча ордера выставляются с залогом, значительно превышающим заданный процент от портфеля.

**Пример проблемы:**
- Пользователь хочет рисковать 1% портфеля ($1000 баланс = $10 залог)
- Плечо: 10x
- Минимальный лот требует номинал $1000
- Фактический залог: $1000 / 10 = $100 (10% портфеля вместо 1%)

## Решение

Реализована система автоматической корректировки плеча, которая:

1. **Анализирует фактический залог** после применения минимальных требований биржи
2. **Автоматически увеличивает плечо** если фактический залог превышает целевой
3. **Соблюдает лимиты безопасности** (минимальное и максимальное плечо)
4. **Логирует все корректировки** для прозрачности

## Как это работает

### 1. Расчет целевого залога
```python
target_margin_usd = total_balance * (portfolio_percent / 100.0)
```

### 2. Применение минимальных требований биржи
```python
position_size = max(min_amount, calculated_position_size)
```

### 3. Проверка необходимости корректировки
```python
actual_margin_with_original_leverage = (position_size * entry_price) / original_leverage
if actual_margin_with_original_leverage > target_margin_usd:
    # Нужна корректировка плеча
```

### 4. Расчет скорректированного плеча
```python
required_leverage = (position_size * entry_price) / target_margin_usd
adjusted_leverage = min(max_leverage, max(min_leverage, int(required_leverage)))
```

## Примеры работы

### Пример 1: Нормальная ситуация
- **Целевой залог:** $10 (1% от $1000)
- **Минимальный лот:** 0.001 BTC × $50,000 = $50
- **Исходное плечо:** 10x
- **Результат:** Плечо остается 10x, залог $5 (0.5% портфеля)

### Пример 2: Проблематичная ситуация
- **Целевой залог:** $10 (1% от $1000)
- **Минимальный лот:** 10M токенов × $0.0001 = $1000
- **Исходное плечо:** 10x
- **Без корректировки:** Залог $100 (10% портфеля) ❌
- **С корректировкой:** Плечо 100x, залог $10 (1% портфеля) ✅

## Безопасность

### Ограничения плеча
- **Минимальное плечо:** 1x (по умолчанию)
- **Максимальное плечо:** 100x (по умолчанию, настраивается)
- **Никогда не уменьшает плечо** ниже исходного значения

### Логирование
Все корректировки плеча логируются с подробной информацией:
```
⚙️ КОРРЕКТИРОВКА ПЛЕЧА для SYMBOL:
   Целевой залог: $10.00 USD (1.0%)
   Фактический номинал: $1000.00 USD
   Исходное плечо: 10x → Скорректированное: 100x
   Фактический залог: $10.00 USD (1.0%)
```

### Уведомления в чат
При корректировке плеча отправляется уведомление:
```
⚙️ ПЛЕЧО АВТОМАТИЧЕСКИ СКОРРЕКТИРОВАНО

Символ: SYMBOL
Исходное плечо: 10x
Скорректированное плечо: 100x

Причина: Размер позиции был увеличен из-за минимальных требований биржи.
Плечо увеличено для соблюдения лимита портфеля (1%).
```

## Технические детали

### Новые функции
- `_calculate_adjusted_leverage()` - расчет скорректированного плеча
- Обновленная `_calculate_position_size()` - возвращает также скорректированное плечо
- Обновленная логика установки плеча в `execute_signal()`

### Изменения в API
```python
# Старая сигнатура
position_size, actual_portfolio_percent = await _calculate_position_size(...)

# Новая сигнатура  
position_size, actual_portfolio_percent, adjusted_leverage = await _calculate_position_size(...)
```

### Параметры корректировки
```python
def _calculate_adjusted_leverage(
    self, 
    target_margin_usd: float,      # Целевой залог
    position_size: float,          # Размер позиции после применения минимумов
    entry_price: float,            # Цена входа
    original_leverage: int,        # Исходное плечо
    min_leverage: int = 1,         # Минимальное плечо
    max_leverage: int = 100        # Максимальное плечо
) -> int:
```

## Тестирование

Функциональность протестирована на различных сценариях:
- ✅ Нормальные торговые пары
- ✅ Пары с большими лотами  
- ✅ Экстремальные случаи
- ✅ Граничные значения
- ✅ Обработка ошибок

Запуск тестов:
```bash
python test_leverage_adjustment.py
```

## Заключение

Новая функциональность решает проблему превышения лимитов портфеля для торговых пар с большими минимальными лотами, автоматически корректируя плечо для соблюдения заданного процента риска от портфеля.
