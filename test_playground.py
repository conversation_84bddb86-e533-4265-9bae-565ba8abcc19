#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы биржи-песочницы
"""

import asyncio
import os
from dotenv import load_dotenv

from playground_exchange import PlaygroundExchange
from playground_bot import PlaygroundBot
from signal_analyzer import TradingSignal, SignalDirection


class MockLogger:
    """Простой логгер для тестирования"""
    
    async def info(self, message):
        print(f"INFO: {message}")
    
    async def success(self, message):
        print(f"SUCCESS: {message}")
    
    async def warning(self, message):
        print(f"WARNING: {message}")
    
    async def error(self, message, exception=None):
        print(f"ERROR: {message}")
        if exception:
            print(f"Exception: {exception}")


async def test_playground_exchange():
    """Тестирует базовую функциональность PlaygroundExchange"""
    print("🎮 Тестирование PlaygroundExchange...")
    
    logger = MockLogger()
    exchange = PlaygroundExchange(logger=logger)
    
    # Инициализация
    if not await exchange.initialize():
        print("❌ Не удалось инициализировать playground exchange")
        return False
    
    # Создание тестового сигнала
    signal = TradingSignal(
        direction=SignalDirection.LONG,
        ticker="BTC/USDT",
        entry_price=50000.0,
        leverage=10,
        take_profits=[52000.0, 54000.0, 56000.0],
        stop_loss=48000.0
    )
    
    # Выполнение сигнала
    trade = await exchange.execute_signal(
        signal=signal,
        source_channel="Test Channel",
        max_volume_usd=100.0,
        portfolio_percent=1.0
    )
    
    if trade:
        print(f"✅ Сделка создана: {trade.id}")
        print(f"   Символ: {trade.entry_order.symbol}")
        print(f"   Размер: {trade.entry_order.filled_amount}")
        print(f"   Цена входа: {trade.entry_price}")
    else:
        print("❌ Не удалось создать сделку")
        return False
    
    # Получение статистики
    stats = exchange.get_stats()
    print(f"📊 Статистика: {stats}")
    
    return True


async def test_playground_bot():
    """Тестирует PlaygroundBot"""
    print("\n🎮 Тестирование PlaygroundBot...")
    
    # Загружаем переменные окружения
    load_dotenv()
    
    logger = MockLogger()
    bot = PlaygroundBot(
        api_key=os.getenv('BINGX_API_KEY', 'test_key'),
        api_secret=os.getenv('BINGX_API_SECRET', 'test_secret'),
        leverage=10,
        testnet=True,
        logger=logger
    )
    
    # Инициализация
    if not await bot.initialize():
        print("❌ Не удалось инициализировать playground bot")
        return False
    
    # Создание тестового сигнала
    signal = TradingSignal(
        direction=SignalDirection.SHORT,
        ticker="ETH/USDT",
        entry_price=3000.0,
        leverage=5,
        take_profits=[2900.0, 2800.0],
        stop_loss=3100.0
    )
    
    # Выполнение сигнала
    trade = await bot.execute_signal(
        signal=signal,
        source_channel="Test Playground Channel",
        max_volume_usd=50.0,
        portfolio_percent=0.5
    )
    
    if trade:
        print(f"✅ Playground сделка создана: {trade.id}")
        print(f"   Направление: {signal.direction}")
        print(f"   Символ: {signal.ticker}")
        print(f"   Размер: {trade.entry_order.filled_amount}")
    else:
        print("❌ Не удалось создать playground сделку")
        return False
    
    # Получение статистики
    stats = bot.get_playground_stats()
    print(f"📊 Статистика бота: {stats}")
    
    return True


async def test_price_simulation():
    """Тестирует симуляцию изменения цен"""
    print("\n🎮 Тестирование симуляции цен...")
    
    logger = MockLogger()
    exchange = PlaygroundExchange(logger=logger)
    
    if not await exchange.initialize():
        print("❌ Не удалось инициализировать playground exchange")
        return False
    
    # Создаем сделку
    signal = TradingSignal(
        direction=SignalDirection.LONG,
        ticker="BTC/USDT",
        entry_price=50000.0,
        leverage=10,
        take_profits=[51000.0],  # Близкий TP для быстрого тестирования
        stop_loss=49000.0
    )
    
    trade = await exchange.execute_signal(
        signal=signal,
        source_channel="Price Test Channel",
        max_volume_usd=100.0
    )
    
    if not trade:
        print("❌ Не удалось создать сделку для тестирования цен")
        return False
    
    print(f"✅ Сделка создана для тестирования: {trade.id}")
    
    # Симулируем изменение цены (вручную для теста)
    exchange.current_prices['BTC-USDT'] = 51500.0  # Цена выше TP
    
    # Обновляем позицию
    position = exchange.positions.get(trade.id)
    if position:
        position.current_price = 51500.0
        position.unrealized_pnl = exchange._calculate_unrealized_pnl(position)
        print(f"📈 Цена обновлена до ${position.current_price}")
        print(f"💰 Unrealized PnL: ${position.unrealized_pnl}")
    
    # Проверяем ордера
    await exchange._check_position_orders(trade.id, position)
    
    return True


async def main():
    """Главная функция тестирования"""
    print("🚀 Запуск тестов биржи-песочницы\n")
    
    try:
        # Тест 1: Базовая функциональность exchange
        if not await test_playground_exchange():
            print("❌ Тест PlaygroundExchange провален")
            return
        
        # Тест 2: Функциональность бота
        if not await test_playground_bot():
            print("❌ Тест PlaygroundBot провален")
            return
        
        # Тест 3: Симуляция цен
        if not await test_price_simulation():
            print("❌ Тест симуляции цен провален")
            return
        
        print("\n✅ Все тесты пройдены успешно!")
        print("🎮 Биржа-песочница готова к использованию")
        
    except Exception as e:
        print(f"\n❌ Ошибка во время тестирования: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
