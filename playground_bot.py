"""
Торговый бот для биржи-песочницы
Наследует от BingXTradingBot, но использует PlaygroundExchange вместо реальной биржи
"""

from typing import Optional, Dict, List
from datetime import datetime

from trading_bot import BingXTradingBot, TradeInstance, OpenMode
from signal_analyzer import TradingSignal
from playground_exchange import PlaygroundExchange


class PlaygroundBot(BingXTradingBot):
    """Торговый бот для биржи-песочницы"""
    
    def __init__(self, api_key: str, api_secret: str, leverage: int = 10, testnet: bool = True, logger=None):
        # Инициализируем родительский класс
        super().__init__(api_key, api_secret, leverage, testnet, logger)
        
        # Заменяем exchange на playground
        self.playground_exchange = None
        self.is_playground = True
    
    async def initialize(self):
        """Инициализация playground бота"""
        try:
            # Создаем и инициализируем playground exchange
            self.playground_exchange = PlaygroundExchange(logger=self.logger)
            
            if not await self.playground_exchange.initialize():
                if self.logger:
                    await self.logger.error("❌ Не удалось инициализировать playground exchange")
                return False
            
            # Заменяем exchange на playground
            self.exchange = self.playground_exchange
            
            if self.logger:
                await self.logger.success("✅ Playground торговый бот инициализирован")
            
            return True
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка инициализации playground бота: {str(e)}", e)
            return False
    
    async def execute_signal(self, signal: TradingSignal, source_channel: str, max_volume_usd: float, 
                           portfolio_percent: Optional[float] = None, max_portfolio_usage: float = 100, 
                           open_mode: OpenMode = OpenMode.DEFAULT, move_stop_to_breakeven: bool = True, 
                           max_take_profit_percent: float = 0.0, position_lifetime: str = "0s") -> Optional[TradeInstance]:
        """Выполняет торговый сигнал в playground"""
        try:
            if not self.playground_exchange:
                if self.logger:
                    await self.logger.error("Playground exchange не инициализирован")
                return None
            
            if self.logger:
                await self.logger.info(f"🎮 Выполнение playground сигнала: {signal.direction} {signal.ticker}")
            
            # Используем playground exchange для выполнения сигнала
            trade = await self.playground_exchange.execute_signal(
                signal=signal,
                source_channel=source_channel,
                max_volume_usd=max_volume_usd,
                portfolio_percent=portfolio_percent
            )
            
            if trade:
                # Добавляем сделку в активные сделки бота
                self.active_trades[trade.id] = trade
                
                if self.logger:
                    await self.logger.success(f"🎮 Playground сделка создана: {trade.id}")
            
            return trade
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка выполнения playground сигнала: {str(e)}", e)
            return None
    
    async def _get_usdt_balance(self) -> float:
        """Возвращает симулированный баланс для playground"""
        return 10000.0  # Фиксированный баланс $10,000 для playground
    
    async def get_available_symbols(self) -> List[str]:
        """Возвращает список доступных символов (упрощенная версия для playground)"""
        # Возвращаем популярные торговые пары
        return [
            'BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'SOL-USDT',
            'XRP-USDT', 'DOT-USDT', 'DOGE-USDT', 'AVAX-USDT', 'MATIC-USDT',
            'LINK-USDT', 'UNI-USDT', 'LTC-USDT', 'BCH-USDT', 'ATOM-USDT'
        ]
    
    async def sync_positions_with_exchange(self) -> List[Dict]:
        """Синхронизация с playground exchange (заглушка)"""
        return []  # В playground нет внешних позиций для синхронизации
    
    def get_playground_stats(self) -> Dict:
        """Возвращает статистику playground бота"""
        try:
            stats = {
                'bot_type': 'playground',
                'active_trades': len(self.active_trades),
                'simulated_balance': 10000.0,
                'is_playground': True
            }
            
            if self.playground_exchange:
                playground_stats = self.playground_exchange.get_stats()
                stats.update(playground_stats)
            
            return stats
            
        except Exception as e:
            if self.logger:
                import asyncio
                asyncio.create_task(self.logger.error(f"Ошибка получения playground статистики: {str(e)}", e))
            return {'bot_type': 'playground', 'error': str(e)}
    
    async def start_monitoring(self):
        """Запускает мониторинг playground биржи"""
        if self.playground_exchange:
            await self.playground_exchange.start_monitoring()
    
    def stop_monitoring(self):
        """Останавливает мониторинг playground биржи"""
        if self.playground_exchange:
            self.playground_exchange.stop_monitoring()
    
    # Переопределяем методы, которые не должны работать с реальной биржей
    
    async def _place_entry_order(self, *args, **kwargs):
        """Заглушка - ордера размещаются через playground_exchange"""
        pass
    
    async def _place_stop_loss_order(self, *args, **kwargs):
        """Заглушка - ордера размещаются через playground_exchange"""
        pass
    
    async def _place_take_profit_orders(self, *args, **kwargs):
        """Заглушка - ордера размещаются через playground_exchange"""
        pass
    
    async def _close_position_by_market(self, *args, **kwargs):
        """Заглушка - позиции закрываются через playground_exchange"""
        pass
    
    async def _cancel_order(self, *args, **kwargs):
        """Заглушка - ордера отменяются через playground_exchange"""
        pass
    
    async def _get_open_positions_from_exchange(self):
        """Возвращает позиции из playground"""
        if self.playground_exchange:
            return list(self.playground_exchange.positions.values())
        return []
    
    async def _log_to_chat(self, message: str):
        """Логирует сообщение в чат с пометкой playground"""
        try:
            # Добавляем префикс playground к сообщению
            playground_message = f"🎮 PLAYGROUND: {message}"
            
            # Используем метод playground_exchange для отправки
            if self.playground_exchange:
                await self.playground_exchange._send_to_chat(playground_message)
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка отправки playground сообщения: {str(e)}", e)
    
    def __str__(self):
        return f"PlaygroundBot(leverage={self.leverage}, active_trades={len(self.active_trades)})"
    
    def __repr__(self):
        return self.__str__()
