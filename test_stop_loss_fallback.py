#!/usr/bin/env python3
"""
Тест для проверки фолбэк логики stop loss
"""

import sys
import os
from unittest.mock import Mock, AsyncMock
from enum import Enum

# Мокаем все зависимости
sys.modules['ccxt'] = Mock()
sys.modules['index_v3'] = Mock()
sys.modules['pydantic'] = Mock()
sys.modules['signal_analyzer'] = Mock()

# Создаем локальный SignalDirection для тестов
class SignalDirection(str, Enum):
    LONG = "Long"
    SHORT = "Short"

# Мокаем signal_analyzer модуль
signal_analyzer_mock = Mock()
signal_analyzer_mock.SignalDirection = SignalDirection
sys.modules['signal_analyzer'] = signal_analyzer_mock

# Импортируем после мокинга
from trading_bot import BingXTradingBot

def test_calculate_fallback_stop_price():
    """Тестирует расчет фолбэк цены stop loss"""
    bot = BingXTradingBot("test_key", "test_secret")
    
    # Тест для LONG позиции
    current_price = 100.0
    
    # Для лонга с отступом -1%
    stop_price_long_1 = bot._calculate_fallback_stop_price(current_price, SignalDirection.LONG, 1.0)
    expected_long_1 = 100.0 * (1 - 1.0/100)  # 99.0
    assert abs(stop_price_long_1 - expected_long_1) < 0.001, f"Expected {expected_long_1}, got {stop_price_long_1}"
    
    # Для лонга с отступом -5%
    stop_price_long_5 = bot._calculate_fallback_stop_price(current_price, SignalDirection.LONG, 5.0)
    expected_long_5 = 100.0 * (1 - 5.0/100)  # 95.0
    assert abs(stop_price_long_5 - expected_long_5) < 0.001, f"Expected {expected_long_5}, got {stop_price_long_5}"
    
    # Тест для SHORT позиции
    # Для шорта с отступом +1%
    stop_price_short_1 = bot._calculate_fallback_stop_price(current_price, SignalDirection.SHORT, 1.0)
    expected_short_1 = 100.0 * (1 + 1.0/100)  # 101.0
    assert abs(stop_price_short_1 - expected_short_1) < 0.001, f"Expected {expected_short_1}, got {stop_price_short_1}"
    
    # Для шорта с отступом +5%
    stop_price_short_5 = bot._calculate_fallback_stop_price(current_price, SignalDirection.SHORT, 5.0)
    expected_short_5 = 100.0 * (1 + 5.0/100)  # 105.0
    assert abs(stop_price_short_5 - expected_short_5) < 0.001, f"Expected {expected_short_5}, got {stop_price_short_5}"
    
    print("✅ Тест _calculate_fallback_stop_price прошел успешно")

def test_fallback_offsets():
    """Тестирует правильность отступов для фолбэк логики"""
    bot = BingXTradingBot("test_key", "test_secret")
    current_price = 1000.0
    
    # Проверяем все отступы для LONG
    expected_offsets = [1.0, 2.0, 3.0, 4.0, 5.0]
    expected_long_prices = [990.0, 980.0, 970.0, 960.0, 950.0]
    
    for i, offset in enumerate(expected_offsets):
        calculated_price = bot._calculate_fallback_stop_price(current_price, SignalDirection.LONG, offset)
        expected_price = expected_long_prices[i]
        assert abs(calculated_price - expected_price) < 0.001, f"LONG offset {offset}%: expected {expected_price}, got {calculated_price}"
    
    # Проверяем все отступы для SHORT
    expected_short_prices = [1010.0, 1020.0, 1030.0, 1040.0, 1050.0]
    
    for i, offset in enumerate(expected_offsets):
        calculated_price = bot._calculate_fallback_stop_price(current_price, SignalDirection.SHORT, offset)
        expected_price = expected_short_prices[i]
        assert abs(calculated_price - expected_price) < 0.001, f"SHORT offset {offset}%: expected {expected_price}, got {calculated_price}"
    
    print("✅ Тест fallback_offsets прошел успешно")

if __name__ == "__main__":
    print("🧪 Запуск тестов фолбэк логики stop loss...")
    
    try:
        test_calculate_fallback_stop_price()
        test_fallback_offsets()
        print("\n🎉 Все тесты прошли успешно!")
        print("\n📋 Реализованная логика:")
        print("1. ✅ Сначала пробуется оригинальный stop loss из сигнала")
        print("2. ✅ При неудаче получается текущая рыночная цена")
        print("3. ✅ Пробуются фолбэк варианты с отступами: -1%, -2%, -3%, -4%, -5%")
        print("4. ✅ При всех неудачах позиция закрывается и отправляется уведомление в errors_chat")
        print("5. ✅ Для LONG: stop loss ниже рыночной цены")
        print("6. ✅ Для SHORT: stop loss выше рыночной цены")
        
    except Exception as e:
        print(f"❌ Тест не прошел: {e}")
        sys.exit(1)
